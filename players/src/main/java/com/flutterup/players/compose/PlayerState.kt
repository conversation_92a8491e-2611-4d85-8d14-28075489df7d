package com.flutterup.players.compose

import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner

/**
 * 播放器状态枚举
 */
enum class PlayerState {
    IDLE,           // 空闲状态
    PREPARING,      // 准备中
    READY,          // 准备完成
    PLAYING,        // 播放中
    PAUSED,         // 暂停
    ENDED,          // 播放结束
    ERROR           // 错误状态
}

/**
 * 播放器错误信息
 */
data class PlayerError(
    val code: Int,
    val message: String,
    val cause: Throwable? = null
)

/**
 * 播放器配置
 */
data class PlayerConfig(
    val autoPlay: Boolean = true,
    val looping: Boolean = false,
    val muted: Boolean = false,
    val showControls: Boolean = true,
    val enableLogging: Boolean = true
)

/**
 * 播放器状态管理基类
 */
@Stable
abstract class BasePlayerState(
    protected val lifecycleOwner: LifecycleOwner
) {
    
    // 播放器状态
    var playerState by mutableStateOf(PlayerState.IDLE)
        protected set
    
    // 播放进度 (0.0 - 1.0)
    var progress by mutableStateOf(0f)
        protected set
    
    // 当前播放时间 (毫秒)
    var currentPosition by mutableStateOf(0L)
        protected set
    
    // 总时长 (毫秒)
    var duration by mutableStateOf(0L)
        protected set
    
    // 是否静音
    var isMuted by mutableStateOf(false)
        protected set
    
    // 音量 (0.0 - 1.0)
    var volume by mutableStateOf(1f)
        protected set
    
    // 错误信息
    var error by mutableStateOf<PlayerError?>(null)
        protected set
    
    // 是否显示控制器
    var showControls by mutableStateOf(true)
        protected set
    
    // 播放器配置
    var config by mutableStateOf(PlayerConfig())
        protected set
    
    // 生命周期观察者
    private val lifecycleObserver = LifecycleEventObserver { _, event ->
        when (event) {
            Lifecycle.Event.ON_PAUSE -> onLifecyclePause()
            Lifecycle.Event.ON_RESUME -> onLifecycleResume()
            Lifecycle.Event.ON_DESTROY -> onLifecycleDestroy()
            else -> {}
        }
    }
    
    init {
        lifecycleOwner.lifecycle.addObserver(lifecycleObserver)
    }
    
    // 抽象方法 - 子类实现具体的播放器操作
    abstract fun play()
    abstract fun pause()
    abstract fun stop()
    abstract fun seekTo(position: Long)
    abstract fun setVolume(volume: Float)
    abstract fun setMuted(muted: Boolean)
    abstract fun setLooping(looping: Boolean)
    abstract fun release()
    
    // 生命周期回调
    protected open fun onLifecyclePause() {
        if (playerState == PlayerState.PLAYING) {
            pause()
        }
    }
    
    protected open fun onLifecycleResume() {
        // 子类可以重写此方法来处理恢复逻辑
    }
    
    protected open fun onLifecycleDestroy() {
        lifecycleOwner.lifecycle.removeObserver(lifecycleObserver)
        release()
    }
    
    // 工具方法
    fun togglePlayPause() {
        when (playerState) {
            PlayerState.PLAYING -> pause()
            PlayerState.PAUSED, PlayerState.READY -> play()
            else -> {}
        }
    }
    
    fun toggleMute() {
        setMuted(!isMuted)
    }
    
    fun formatTime(timeMs: Long): String {
        val totalSeconds = timeMs / 1000
        val minutes = totalSeconds / 60
        val seconds = totalSeconds % 60
        return String.format("%02d:%02d", minutes, seconds)
    }
    
    // 更新进度
    protected fun updateProgress() {
        if (duration > 0) {
            progress = currentPosition.toFloat() / duration.toFloat()
        }
    }
    
    // 设置错误状态
    protected fun setError(code: Int, message: String, cause: Throwable? = null) {
        error = PlayerError(code, message, cause)
        playerState = PlayerState.ERROR
    }
    
    // 清除错误
    protected fun clearError() {
        error = null
    }
}

/**
 * 记住播放器状态的 Compose 函数
 */
@Composable
inline fun <reified T : BasePlayerState> rememberPlayerState(
    crossinline factory: (LifecycleOwner) -> T
): T {
    val lifecycleOwner = LocalLifecycleOwner.current
    return remember(lifecycleOwner) {
        factory(lifecycleOwner)
    }
}
