package com.flutterup.players.compose

import com.ss.ugc.android.alpha_player.model.ScaleType


typealias AlphaPlayerOnVideoSizeChanged = (videoWidth: Int, videoHeight: Int, scaleType: ScaleType) -> Unit

typealias AlphaPlayerOnStartAction = () -> Unit

typealias AlphaPlayerOnEndAction = () -> Unit

typealias AlphaPlayerOnMonitor = (result: <PERSON><PERSON><PERSON>, playType: String, what: Int, extra: Int, errorInfo: String) -> Unit
