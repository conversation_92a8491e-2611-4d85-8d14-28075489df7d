package com.flutterup.players.compose

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.ui.PlayerView

/**
 * Media3 播放器 Compose 组件
 */
@Composable
fun Media3Player(
    modifier: Modifier = Modifier,
    playerState: Media3PlayerState = rememberMedia3PlayerState(),
    mediaUri: String? = null,
    mediaItem: MediaItem? = null,
    config: PlayerConfig = PlayerConfig(),
    onPlayerReady: (() -> Unit)? = null,
    onError: ((PlayerError) -> Unit)? = null
) {
    val context = LocalContext.current
    
    // 更新配置
    LaunchedEffect(config) {
        playerState.updateConfig(config)
    }
    
    // 设置媒体源
    LaunchedEffect(mediaUri, mediaItem) {
        when {
            mediaItem != null -> playerState.setMediaItem(mediaItem)
            mediaUri != null -> playerState.setMediaItem(mediaUri)
        }
    }
    
    // 监听播放器状态变化
    LaunchedEffect(playerState.playerState) {
        when (playerState.playerState) {
            PlayerState.READY -> onPlayerReady?.invoke()
            PlayerState.ERROR -> playerState.error?.let { onError?.invoke(it) }
            else -> {}
        }
    }
    
    Box(modifier = modifier) {
        AndroidView(
            factory = { context ->
                PlayerView(context).apply {
                    useController = config.showControls
                    player = playerState.getExoPlayer()
                }
            },
            update = { playerView ->
                playerView.player = playerState.getExoPlayer()
                playerView.useController = config.showControls
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // 如果不显示默认控制器，可以在这里添加自定义控制器
        if (!config.showControls && playerState.showControls) {
            PlayerControls(
                playerState = playerState,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

/**
 * 简化版 Media3 播放器组件
 */
@Composable
fun SimpleMedia3Player(
    mediaUri: String,
    modifier: Modifier = Modifier,
    autoPlay: Boolean = true,
    looping: Boolean = false,
    showControls: Boolean = true,
    onPlayerReady: (() -> Unit)? = null,
    onError: ((PlayerError) -> Unit)? = null
) {
    Media3Player(
        modifier = modifier,
        mediaUri = mediaUri,
        config = PlayerConfig(
            autoPlay = autoPlay,
            looping = looping,
            showControls = showControls
        ),
        onPlayerReady = onPlayerReady,
        onError = onError
    )
}

/**
 * Media3 播放器组件（使用 MediaItem）
 */
@Composable
fun Media3Player(
    mediaItem: MediaItem,
    modifier: Modifier = Modifier,
    config: PlayerConfig = PlayerConfig(),
    onPlayerReady: (() -> Unit)? = null,
    onError: ((PlayerError) -> Unit)? = null
) {
    Media3Player(
        modifier = modifier,
        mediaItem = mediaItem,
        config = config,
        onPlayerReady = onPlayerReady,
        onError = onError
    )
}
