package com.flutterup.players.compose

import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import com.ss.ugc.android.alpha_player.model.AlphaVideoViewType
import java.util.Locale

/**
 * 播放器工具类
 */
object PlayerUtils {
    
    /**
     * 创建 MediaItem
     */
    fun createMediaItem(
        uri: String,
        title: String? = null,
        subtitle: String? = null,
        description: String? = null
    ): MediaItem {
        val metadata = MediaMetadata.Builder()
            .setTitle(title)
            .setSubtitle(subtitle)
            .setDescription(description)
            .build()
        
        return MediaItem.Builder()
            .setUri(uri)
            .setMediaMetadata(metadata)
            .build()
    }
    
    /**
     * 格式化时间
     */
    fun formatTime(timeMs: Long): String {
        val totalSeconds = timeMs / 1000
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        
        return if (hours > 0) {
            String.format(Locale.getDefault(), "%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds)
        }
    }
    
    /**
     * 获取视频文件扩展名
     */
    fun getVideoExtension(url: String): String? {
        return url.substringAfterLast('.', "").takeIf { it.isNotEmpty() }
    }
    
    /**
     * 检查是否为支持的视频格式
     */
    fun isSupportedVideoFormat(url: String): Boolean {
        val supportedFormats = setOf("mp4", "mkv", "avi", "mov", "wmv", "flv", "webm", "m4v")
        return getVideoExtension(url)?.lowercase() in supportedFormats
    }
    
    /**
     * 检查是否为 Alpha 视频格式
     */
    fun isAlphaVideoFormat(url: String): Boolean {
        // 这里可以根据实际需求定义 Alpha 视频的识别规则
        return url.contains("alpha", ignoreCase = true) || 
               url.contains("transparent", ignoreCase = true)
    }
    
    /**
     * 根据 URL 推荐播放器类型
     */
    fun recommendPlayerType(videoUrl: String, alphaUrl: String? = null): PlayerType {
        return when {
            alphaUrl != null || isAlphaVideoFormat(videoUrl) -> PlayerType.ALPHA_PLAYER
            isSupportedVideoFormat(videoUrl) -> PlayerType.MEDIA3_PLAYER
            else -> PlayerType.MEDIA3_PLAYER // 默认使用 Media3
        }
    }
    
    /**
     * 根据设备性能推荐 AlphaVideoViewType
     */
    fun recommendAlphaVideoViewType(): AlphaVideoViewType {
        // 这里可以根据设备性能、Android 版本等因素来推荐合适的视图类型
        return AlphaVideoViewType.GL_SURFACE_VIEW // 默认推荐 GL_SURFACE_VIEW
    }
}

/**
 * 播放器类型枚举
 */
enum class PlayerType {
    MEDIA3_PLAYER,
    ALPHA_PLAYER
}

/**
 * 播放器生命周期管理 Composable
 */
@Composable
fun PlayerLifecycleEffect(
    playerState: BasePlayerState,
    onPause: (() -> Unit)? = null,
    onResume: (() -> Unit)? = null,
    onDestroy: (() -> Unit)? = null
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    
    DisposableEffect(lifecycleOwner, playerState) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    if (playerState.playerState == PlayerState.PLAYING) {
                        playerState.pause()
                    }
                    onPause?.invoke()
                }
                Lifecycle.Event.ON_RESUME -> {
                    onResume?.invoke()
                }
                Lifecycle.Event.ON_DESTROY -> {
                    playerState.release()
                    onDestroy?.invoke()
                }
                else -> {}
            }
        }
        
        lifecycleOwner.lifecycle.addObserver(observer)
        
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

/**
 * 播放器错误处理 Composable
 */
@Composable
fun PlayerErrorHandler(
    playerState: BasePlayerState,
    onError: ((PlayerError) -> Unit)? = null
) {
    LaunchedEffect(playerState.error) {
        playerState.error?.let { error ->
            onError?.invoke(error)
        }
    }
}

/**
 * 播放器状态监听 Composable
 */
@Composable
fun PlayerStateListener(
    playerState: BasePlayerState,
    onStateChange: ((PlayerState) -> Unit)? = null,
    onProgressChange: ((Float) -> Unit)? = null,
    onPositionChange: ((Long) -> Unit)? = null
) {
    LaunchedEffect(playerState.playerState) {
        onStateChange?.invoke(playerState.playerState)
    }
    
    LaunchedEffect(playerState.progress) {
        onProgressChange?.invoke(playerState.progress)
    }
    
    LaunchedEffect(playerState.currentPosition) {
        onPositionChange?.invoke(playerState.currentPosition)
    }
}

/**
 * 创建默认播放器配置
 */
fun createDefaultPlayerConfig(
    autoPlay: Boolean = true,
    looping: Boolean = false,
    muted: Boolean = false,
    showControls: Boolean = false,
    enableLogging: Boolean = true
): PlayerConfig {
    return PlayerConfig(
        autoPlay = autoPlay,
        looping = looping,
        muted = muted,
        showControls = showControls,
        enableLogging = enableLogging
    )
}

/**
 * 播放器配置构建器
 */
class PlayerConfigBuilder {
    private var autoPlay: Boolean = true
    private var looping: Boolean = false
    private var muted: Boolean = false
    private var showControls: Boolean = true
    private var enableLogging: Boolean = true
    
    fun autoPlay(autoPlay: Boolean) = apply { this.autoPlay = autoPlay }
    fun looping(looping: Boolean) = apply { this.looping = looping }
    fun muted(muted: Boolean) = apply { this.muted = muted }
    fun showControls(showControls: Boolean) = apply { this.showControls = showControls }
    fun enableLogging(enableLogging: Boolean) = apply { this.enableLogging = enableLogging }
    
    fun build(): PlayerConfig {
        return PlayerConfig(
            autoPlay = autoPlay,
            looping = looping,
            muted = muted,
            showControls = showControls,
            enableLogging = enableLogging
        )
    }
}

/**
 * 创建播放器配置的 DSL 函数
 */
fun playerConfig(builder: PlayerConfigBuilder.() -> Unit): PlayerConfig {
    return PlayerConfigBuilder().apply(builder).build()
}
