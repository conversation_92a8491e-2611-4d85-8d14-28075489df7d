package com.flutterup.players.compose

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay

/**
 * 播放器控制组件
 */
@Composable
fun PlayerControls(
    playerState: BasePlayerState,
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color.Black.copy(alpha = 0.6f),
    contentColor: Color = Color.White,
    autoHideDelay: Long = 3000L // 3秒后自动隐藏
) {
    var isVisible by remember { mutableStateOf(true) }
    var lastInteractionTime by remember { mutableStateOf(System.currentTimeMillis()) }
    
    // 自动隐藏逻辑
    LaunchedEffect(lastInteractionTime, playerState.playerState) {
        if (playerState.playerState == PlayerState.PLAYING) {
            delay(autoHideDelay)
            if (System.currentTimeMillis() - lastInteractionTime >= autoHideDelay) {
                isVisible = false
            }
        } else {
            isVisible = true
        }
    }
    
    // 点击显示控制器
    Box(
        modifier = modifier
            .fillMaxSize()
            .clickable {
                isVisible = !isVisible
                lastInteractionTime = System.currentTimeMillis()
            }
    ) {
        AnimatedVisibility(
            visible = isVisible,
            enter = fadeIn() + slideInVertically(initialOffsetY = { it }),
            exit = fadeOut() + slideOutVertically(targetOffsetY = { it }),
            modifier = Modifier.align(Alignment.BottomCenter)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        backgroundColor,
                        RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                    )
                    .padding(16.dp)
            ) {
                // 进度条
                ProgressBar(
                    progress = playerState.progress,
                    onSeek = { progress ->
                        val position = (playerState.duration * progress).toLong()
                        playerState.seekTo(position)
                        lastInteractionTime = System.currentTimeMillis()
                    },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 控制按钮行
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 时间显示
                    Text(
                        text = "${playerState.formatTime(playerState.currentPosition)} / ${playerState.formatTime(playerState.duration)}",
                        color = contentColor,
                        style = MaterialTheme.typography.bodySmall
                    )
                    
                    // 播放控制按钮
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 播放/暂停按钮
                        ControlButton(
                            icon = if (playerState.playerState == PlayerState.PLAYING) {
                                Icons.Default.Pause
                            } else {
                                Icons.Default.PlayArrow
                            },
                            onClick = {
                                playerState.togglePlayPause()
                                lastInteractionTime = System.currentTimeMillis()
                            },
                            contentColor = contentColor
                        )

                        // 静音按钮
                        ControlButton(
                            icon = if (playerState.isMuted) {
                                Icons.Default.VolumeOff
                            } else {
                                Icons.Default.VolumeUp
                            },
                            onClick = {
                                playerState.toggleMute()
                                lastInteractionTime = System.currentTimeMillis()
                            },
                            contentColor = contentColor
                        )
                    }
                }
            }
        }
    }
}

/**
 * 进度条组件
 */
@Composable
private fun ProgressBar(
    progress: Float,
    onSeek: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Slider(
        value = progress,
        onValueChange = onSeek,
        modifier = modifier,
        colors = SliderDefaults.colors(
            thumbColor = MaterialTheme.colorScheme.primary,
            activeTrackColor = MaterialTheme.colorScheme.primary,
            inactiveTrackColor = Color.White.copy(alpha = 0.3f)
        )
    )
}

/**
 * 控制按钮组件
 */
@Composable
private fun ControlButton(
    icon: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    contentColor: Color = Color.White
) {
    IconButton(
        onClick = onClick,
        modifier = modifier
            .size(48.dp)
            .clip(CircleShape)
            .background(Color.Black.copy(alpha = 0.3f))
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = contentColor,
            modifier = Modifier.size(24.dp)
        )
    }
}

/**
 * 简化版播放器控制组件
 */
@Composable
fun SimplePlayerControls(
    playerState: BasePlayerState,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .background(
                Color.Black.copy(alpha = 0.6f),
                RoundedCornerShape(24.dp)
            )
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 播放/暂停按钮
        ControlButton(
            icon = if (playerState.playerState == PlayerState.PLAYING) {
                Icons.Default.Pause
            } else {
                Icons.Default.PlayArrow
            },
            onClick = { playerState.togglePlayPause() }
        )
        
        // 进度显示
        Text(
            text = "${playerState.formatTime(playerState.currentPosition)} / ${playerState.formatTime(playerState.duration)}",
            color = Color.White,
            style = MaterialTheme.typography.bodySmall
        )
    }
}

/**
 * 中央播放按钮
 */
@Composable
fun CenterPlayButton(
    playerState: BasePlayerState,
    modifier: Modifier = Modifier,
    size: androidx.compose.ui.unit.Dp = 64.dp
) {
    AnimatedVisibility(
        visible = playerState.playerState != PlayerState.PLAYING,
        enter = scaleIn() + fadeIn(),
        exit = scaleOut() + fadeOut(),
        modifier = modifier
    ) {
        IconButton(
            onClick = { playerState.play() },
            modifier = Modifier
                .size(size)
                .clip(CircleShape)
                .background(Color.Black.copy(alpha = 0.6f))
        ) {
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = "Play",
                tint = Color.White,
                modifier = Modifier.size(size * 0.6f)
            )
        }
    }
}
