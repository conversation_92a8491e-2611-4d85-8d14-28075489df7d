package com.flutterup.players.compose

import android.content.Context
import androidx.compose.runtime.*
import androidx.lifecycle.LifecycleOwner
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.VideoSize
import androidx.media3.exoplayer.ExoPlayer
import kotlinx.coroutines.*

/**
 * Media3 播放器状态管理
 */
@Stable
class Media3PlayerState(
    private val context: Context,
    lifecycleOwner: LifecycleOwner
) : BasePlayerState(lifecycleOwner) {
    
    private var exoPlayer: ExoPlayer? = null
    private var progressUpdateJob: Job? = null
    
    // 视频尺寸
    var videoSize by mutableStateOf(VideoSize.UNKNOWN)
        private set
    
    // 播放器监听器
    private val playerListener = object : Player.Listener {
        override fun onPlaybackStateChanged(playbackState: Int) {
            playerState = when (playbackState) {
                Player.STATE_IDLE -> PlayerState.IDLE
                Player.STATE_BUFFERING -> PlayerState.PREPARING
                Player.STATE_READY -> if (exoPlayer?.playWhenReady == true) PlayerState.PLAYING else PlayerState.READY
                Player.STATE_ENDED -> PlayerState.ENDED
                else -> PlayerState.IDLE
            }
        }
        
        override fun onPlayWhenReadyChanged(playWhenReady: Boolean, reason: Int) {
            if (playWhenReady && exoPlayer?.playbackState == Player.STATE_READY) {
                playerState = PlayerState.PLAYING
                startProgressUpdate()
            } else {
                if (playerState == PlayerState.PLAYING) {
                    playerState = PlayerState.PAUSED
                }
                stopProgressUpdate()
            }
        }
        
        override fun onPlayerError(error: PlaybackException) {
            setError(
                code = error.errorCode,
                message = error.message ?: "Unknown player error",
                cause = error
            )
        }
        
        override fun onVideoSizeChanged(videoSize: VideoSize) {
            <EMAIL> = videoSize
        }
    }
    
    /**
     * 初始化播放器
     */
    fun initialize() {
        if (exoPlayer == null) {
            exoPlayer = ExoPlayer.Builder(context).build().apply {
                addListener(playerListener)
                playWhenReady = config.autoPlay
                repeatMode = if (config.looping) Player.REPEAT_MODE_ONE else Player.REPEAT_MODE_OFF
            }
        }
    }
    
    /**
     * 设置媒体源
     */
    fun setMediaItem(uri: String) {
        initialize()
        exoPlayer?.let { player ->
            val mediaItem = MediaItem.fromUri(uri)
            player.setMediaItem(mediaItem)
            player.prepare()
            clearError()
        }
    }
    
    /**
     * 设置媒体源
     */
    fun setMediaItem(mediaItem: MediaItem) {
        initialize()
        exoPlayer?.let { player ->
            player.setMediaItem(mediaItem)
            player.prepare()
            clearError()
        }
    }
    
    override fun play() {
        exoPlayer?.playWhenReady = true
    }
    
    override fun pause() {
        exoPlayer?.playWhenReady = false
    }
    
    override fun stop() {
        exoPlayer?.stop()
        stopProgressUpdate()
    }
    
    override fun seekTo(position: Long) {
        exoPlayer?.seekTo(position)
    }
    
    override fun updateVolume(volume: Float) {
        this.volume = volume.coerceIn(0f, 1f)
        exoPlayer?.volume = this.volume
    }

    override fun updateMuted(muted: Boolean) {
        this.isMuted = muted
        exoPlayer?.volume = if (muted) 0f else volume
    }
    
    override fun setLooping(looping: Boolean) {
        config = config.copy(looping = looping)
        exoPlayer?.repeatMode = if (looping) Player.REPEAT_MODE_ONE else Player.REPEAT_MODE_OFF
    }
    
    override fun release() {
        stopProgressUpdate()
        exoPlayer?.removeListener(playerListener)
        exoPlayer?.release()
        exoPlayer = null
    }
    
    /**
     * 获取 ExoPlayer 实例
     */
    fun getExoPlayer(): ExoPlayer? = exoPlayer
    
    /**
     * 更新配置
     */
    fun updateConfig(newConfig: PlayerConfig) {
        config = newConfig
        exoPlayer?.let { player ->
            player.playWhenReady = newConfig.autoPlay
            player.repeatMode = if (newConfig.looping) Player.REPEAT_MODE_ONE else Player.REPEAT_MODE_OFF
            updateVolume(if (newConfig.muted) 0f else volume)
        }
        showControls = newConfig.showControls
    }
    
    // 开始进度更新
    private fun startProgressUpdate() {
        stopProgressUpdate()
        progressUpdateJob = CoroutineScope(Dispatchers.Main).launch {
            while (isActive) {
                exoPlayer?.let { player ->
                    currentPosition = player.currentPosition
                    duration = player.duration.takeIf { it > 0 } ?: 0L
                    updateProgress()
                }
                delay(100) // 每100ms更新一次
            }
        }
    }
    
    // 停止进度更新
    private fun stopProgressUpdate() {
        progressUpdateJob?.cancel()
        progressUpdateJob = null
    }
}

/**
 * 记住 Media3 播放器状态
 */
@Composable
fun rememberMedia3PlayerState(
    context: Context = androidx.compose.ui.platform.LocalContext.current
): Media3PlayerState {
    return rememberPlayerState { lifecycleOwner ->
        Media3PlayerState(context, lifecycleOwner)
    }
}
