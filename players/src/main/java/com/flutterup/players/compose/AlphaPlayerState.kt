package com.flutterup.players.compose

import android.content.Context
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.LifecycleOwner
import com.flutterup.players.view.AlphaExoPlayerView
import com.ss.ugc.android.alpha_player.IMonitor
import com.ss.ugc.android.alpha_player.IPlayerAction
import com.ss.ugc.android.alpha_player.model.AlphaVideoViewType
import com.ss.ugc.android.alpha_player.model.ScaleType
import kotlinx.coroutines.*

/**
 * AlphaPlayer 播放器状态管理
 */
@Stable
class AlphaPlayerState(
    private val context: Context,
    lifecycleOwner: LifecycleOwner
) : BasePlayerState(lifecycleOwner) {
    
    private var alphaPlayerView: AlphaExoPlayerView? = null

    private var alphaPlayerOnVideoSizeChanged: AlphaPlayerOnVideoSizeChanged? = null

    private var alphaPlayerOnStartAction: AlphaPlayerOnStartAction? = null

    private var alphaPlayerOnEndAction: AlphaPlayerOnEndAction? = null

    private var alphaPlayerOnMonitor: AlphaPlayerOnMonitor? = null

    private var progressUpdateJob: Job? = null
    
    // Alpha 视频类型
    var videoViewType by mutableStateOf(AlphaVideoViewType.GL_TEXTURE_VIEW)
        private set
    
    // 是否已初始化
    var isInitialized by mutableStateOf(false)
        private set
    
    // 当前播放的视频路径
    var currentVideoPath by mutableStateOf<String?>(null)
        private set

    fun updatePlayerView(view: AlphaExoPlayerView) {
        if (alphaPlayerView == null) {
            alphaPlayerView = view
        }
    }

    /**
     * 初始化 AlphaPlayer
     */
    fun initialize(videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_TEXTURE_VIEW) {
        requireNotNull(alphaPlayerView)

        if (!isInitialized) {
            this.videoViewType = videoViewType

            alphaPlayerView?.initPlayerController(
                context = context,
                owner = lifecycleOwner,
                alphaVideoViewType = videoViewType,
                playerAction = createPlayerAction(),
                monitor = createMonitor()
            )

            isInitialized = true
        }
    }
    
    /**
     * 设置视频源
     */
    fun setVideoSource(videoPath: String) {
        alphaPlayerView?.let { view ->
            try {
                playerState = PlayerState.PREPARING
                clearError()
                currentVideoPath = videoPath
                
                // 附加视图
                view.attachView()
                
                // 开始播放视频
                view.startVideo(videoPath)
                
            } catch (e: Exception) {
                setError(
                    code = -1,
                    message = "Failed to set video source: ${e.message}",
                    cause = e
                )
            }
        }
    }
    
    override fun play() {
        // AlphaPlayer 通常在 startVideoGift 后自动播放
        // 这里可以添加恢复播放的逻辑
        playerState = PlayerState.PLAYING
        startProgressUpdate()
    }
    
    override fun pause() {
        // AlphaPlayer 可能不支持暂停，这里设置状态
        playerState = PlayerState.PAUSED
        stopProgressUpdate()
    }
    
    override fun stop() {
        alphaPlayerView?.detachView()
        playerState = PlayerState.IDLE
        stopProgressUpdate()
        currentVideoPath = null
    }
    
    override fun seekTo(position: Long) {
        // AlphaPlayer 通常不支持 seek 操作
        // 这里只更新当前位置状态
        currentPosition = position
        updateProgress()
    }
    
    override fun updateVolume(volume: Float) {
        this.volume = volume.coerceIn(0f, 1f)
        // AlphaPlayer 的音量控制需要通过底层 ExoPlayer 实现
    }
    
    override fun updateMuted(muted: Boolean) {
        this.isMuted = muted
        // AlphaPlayer 的静音控制需要通过底层 ExoPlayer 实现
    }
    
    override fun setLooping(looping: Boolean) {
        config = config.copy(looping = looping)
        // AlphaPlayer 的循环播放在 DataSource 中设置
    }
    
    override fun release() {
        stopProgressUpdate()
        alphaPlayerView?.let { view ->
            view.detachView()
            view.releasePlayerController()
        }
        alphaPlayerView = null
        isInitialized = false
        currentVideoPath = null
    }
    
    /**
     * 获取 AlphaExoPlayerView 实例
     */
    fun getAlphaPlayerView(): AlphaExoPlayerView? = alphaPlayerView
    
    /**
     * 更新配置
     */
    fun updateConfig(newConfig: PlayerConfig) {
        config = newConfig
        showControls = newConfig.showControls
    }

    fun setOnVideoSizeChangedListener(listener: AlphaPlayerOnVideoSizeChanged?) {
        alphaPlayerOnVideoSizeChanged = listener
    }

    fun setOnStartActionListener(listener: AlphaPlayerOnStartAction?) {
        alphaPlayerOnStartAction = listener
    }

    fun setOnEndActionListener(listener: AlphaPlayerOnEndAction?) {
        alphaPlayerOnEndAction = listener
    }

    fun setOnMonitorListener(listener: AlphaPlayerOnMonitor?) {
        alphaPlayerOnMonitor = listener
    }
    
    /**
     * 创建播放器动作监听器
     */
    private fun createPlayerAction(): IPlayerAction {
        return object : IPlayerAction {
            override fun onVideoSizeChanged(videoWidth: Int, videoHeight: Int, scaleType: ScaleType) {
                // 视频尺寸变化回调
                alphaPlayerOnVideoSizeChanged?.invoke(videoWidth, videoHeight, scaleType)
            }

            override fun startAction() {
                playerState = PlayerState.PLAYING
                startProgressUpdate()
                alphaPlayerOnStartAction?.invoke()
            }

            override fun endAction() {
                playerState = PlayerState.ENDED
                stopProgressUpdate()
                alphaPlayerOnEndAction?.invoke()
            }
        }
    }
    
    /**
     * 创建监听器
     */
    private fun createMonitor(): IMonitor {
        return object : IMonitor {
            override fun monitor(result: Boolean, playType: String, what: Int, extra: Int, errorInfo: String) {
                if (!result) {
                    setError(
                        code = what,
                        message = "AlphaPlayer error: $errorInfo (extra: $extra)"
                    )
                } else {
                    when (what) {
                        // 可以根据 what 参数判断不同的状态
                        else -> {
                            if (playerState == PlayerState.PREPARING) {
                                playerState = PlayerState.READY
                                if (config.autoPlay) {
                                    play()
                                }
                            }
                        }
                    }
                }
                alphaPlayerOnMonitor?.invoke(result, playType, what, extra, errorInfo)
            }
        }
    }
    
    // 开始进度更新
    private fun startProgressUpdate() {
        stopProgressUpdate()
        progressUpdateJob = CoroutineScope(Dispatchers.Main).launch {
            while (isActive && playerState == PlayerState.PLAYING) {
                // AlphaPlayer 的进度更新通过 onVideoRender 回调处理
                // 这里可以添加额外的进度更新逻辑
                delay(100)
            }
        }
    }
    
    // 停止进度更新
    private fun stopProgressUpdate() {
        progressUpdateJob?.cancel()
        progressUpdateJob = null
    }
}

/**
 * 记住 AlphaPlayer 播放器状态
 */
@Composable
fun rememberAlphaPlayerState(
    context: Context = LocalContext.current,
): AlphaPlayerState {
    return rememberPlayerState { lifecycleOwner ->
        AlphaPlayerState(context, lifecycleOwner)
    }
}
