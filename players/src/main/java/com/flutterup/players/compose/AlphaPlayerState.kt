package com.flutterup.players.compose

import android.content.Context
import android.view.Surface
import androidx.compose.runtime.*
import androidx.lifecycle.LifecycleOwner
import com.flutterup.players.impl.AlphaExoPlayerImpl
import com.ss.ugc.android.alpha_player.IAlphaVideoView
import com.ss.ugc.android.alpha_player.model.AlphaVideoViewType
import com.ss.ugc.android.alpha_player.model.VideoInfo
import com.ss.ugc.android.alpha_player.widget.AlphaVideoView
import kotlinx.coroutines.*

/**
 * AlphaPlayer 播放器状态管理
 */
@Stable
class AlphaPlayerState(
    private val context: Context,
    lifecycleOwner: LifecycleOwner
) : BasePlayerState(lifecycleOwner) {
    
    private var alphaVideoView: AlphaVideoView? = null
    private var alphaPlayer: AlphaExoPlayerImpl? = null
    private var progressUpdateJob: kotlinx.coroutines.Job? = null
    
    // 视频信息
    var videoInfo by mutableStateOf<VideoInfo?>(null)
        private set
    
    // Alpha 视频类型
    var videoViewType by mutableStateOf(AlphaVideoViewType.GL_SURFACE_VIEW)
        private set
    
    /**
     * 初始化 AlphaPlayer
     */
    fun initialize(videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_SURFACE_VIEW) {
        if (alphaVideoView == null) {
            this.videoViewType = videoViewType
            
            alphaVideoView = AlphaVideoView(context).apply {
                setVideoViewType(videoViewType)
                
                // 设置播放器实现
                alphaPlayer = AlphaExoPlayerImpl(context).also { player ->
                    setPlayerAction(player)
                    
                    // 设置监听器
                    player.setOnPreparedListener {
                        playerState = PlayerState.READY
                        videoInfo = player.getVideoInfo()
                        if (config.autoPlay) {
                            play()
                        }
                    }
                    
                    player.setOnCompletionListener {
                        playerState = PlayerState.ENDED
                        stopProgressUpdate()
                        if (config.looping) {
                            seekTo(0)
                            play()
                        }
                    }
                    
                    player.setOnErrorListener { _, _, message ->
                        setError(
                            code = -1,
                            message = message ?: "Unknown alpha player error"
                        )
                        false
                    }
                    
                    player.setOnFirstFrameListener {
                        if (playerState == PlayerState.PREPARING) {
                            playerState = PlayerState.PLAYING
                            startProgressUpdate()
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 设置视频源
     */
    fun setVideoSource(videoPath: String, alphaPath: String? = null) {
        initialize()
        alphaVideoView?.let { view ->
            try {
                playerState = PlayerState.PREPARING
                clearError()
                
                if (alphaPath != null) {
                    // 带 Alpha 通道的视频
                    view.startPlay(videoPath, alphaPath)
                } else {
                    // 普通视频
                    view.startPlay(videoPath)
                }
            } catch (e: Exception) {
                setError(
                    code = -1,
                    message = "Failed to set video source: ${e.message}",
                    cause = e
                )
            }
        }
    }
    
    override fun play() {
        alphaPlayer?.start()
        if (playerState == PlayerState.READY || playerState == PlayerState.PAUSED) {
            playerState = PlayerState.PLAYING
            startProgressUpdate()
        }
    }
    
    override fun pause() {
        alphaPlayer?.pause()
        if (playerState == PlayerState.PLAYING) {
            playerState = PlayerState.PAUSED
            stopProgressUpdate()
        }
    }
    
    override fun stop() {
        alphaPlayer?.stop()
        alphaVideoView?.stopPlay()
        playerState = PlayerState.IDLE
        stopProgressUpdate()
    }
    
    override fun seekTo(position: Long) {
        // AlphaPlayer 可能不支持 seek，这里提供接口但可能无效果
        currentPosition = position
        updateProgress()
    }
    
    override fun setVolume(volume: Float) {
        this.volume = volume.coerceIn(0f, 1f)
        // AlphaPlayer 基于 ExoPlayer，可以通过底层 ExoPlayer 设置音量
        // 这里需要根据实际 AlphaPlayer API 调整
    }
    
    override fun setMuted(muted: Boolean) {
        this.isMuted = muted
        // 实现静音逻辑
    }
    
    override fun setLooping(looping: Boolean) {
        config = config.copy(looping = looping)
        alphaPlayer?.setLooping(looping)
    }
    
    override fun release() {
        stopProgressUpdate()
        alphaVideoView?.stopPlay()
        alphaPlayer?.release()
        alphaVideoView = null
        alphaPlayer = null
    }
    
    /**
     * 获取 AlphaVideoView 实例
     */
    fun getAlphaVideoView(): AlphaVideoView? = alphaVideoView
    
    /**
     * 获取 AlphaPlayer 实例
     */
    fun getAlphaPlayer(): AlphaExoPlayerImpl? = alphaPlayer
    
    /**
     * 设置表面
     */
    fun setSurface(surface: Surface) {
        alphaPlayer?.setSurface(surface)
    }
    
    /**
     * 更新配置
     */
    fun updateConfig(newConfig: PlayerConfig) {
        config = newConfig
        alphaPlayer?.setLooping(newConfig.looping)
        showControls = newConfig.showControls
    }
    
    // 开始进度更新
    private fun startProgressUpdate() {
        stopProgressUpdate()
        progressUpdateJob = CoroutineScope(Dispatchers.Main).launch {
            while (isActive && playerState == PlayerState.PLAYING) {
                // AlphaPlayer 可能没有直接的进度获取方法
                // 这里使用估算的方式，实际使用时需要根据 AlphaPlayer API 调整
                currentPosition += 100
                videoInfo?.let { info ->
                    // 如果有视频时长信息，可以计算进度
                    // 这里需要根据实际 VideoInfo 结构调整
                }
                updateProgress()
                delay(100)
            }
        }
    }
    
    // 停止进度更新
    private fun stopProgressUpdate() {
        progressUpdateJob?.cancel()
        progressUpdateJob = null
    }
}

/**
 * 记住 AlphaPlayer 播放器状态
 */
@Composable
fun rememberAlphaPlayerState(
    context: Context = androidx.compose.ui.platform.LocalContext.current
): AlphaPlayerState {
    return rememberPlayerState { lifecycleOwner ->
        AlphaPlayerState(context, lifecycleOwner)
    }
}
