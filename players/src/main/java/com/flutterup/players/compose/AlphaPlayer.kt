package com.flutterup.players.compose

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.ss.ugc.android.alpha_player.model.AlphaVideoViewType

/**
 * AlphaPlayer 播放器 Compose 组件
 */
@Composable
fun AlphaPlayer(
    modifier: Modifier = Modifier,
    playerState: AlphaPlayerState = rememberAlphaPlayerState(),
    videoPath: String? = null,
    alphaPath: String? = null,
    videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_SURFACE_VIEW,
    config: PlayerConfig = PlayerConfig(),
    onPlayerReady: (() -> Unit)? = null,
    onError: ((PlayerError) -> Unit)? = null
) {
    val context = LocalContext.current
    
    // 初始化播放器
    LaunchedEffect(videoViewType) {
        playerState.initialize(videoViewType)
    }
    
    // 更新配置
    LaunchedEffect(config) {
        playerState.updateConfig(config)
    }
    
    // 设置视频源
    LaunchedEffect(videoPath, alphaPath) {
        if (videoPath != null) {
            playerState.setVideoSource(videoPath, alphaPath)
        }
    }
    
    // 监听播放器状态变化
    LaunchedEffect(playerState.playerState) {
        when (playerState.playerState) {
            PlayerState.READY -> onPlayerReady?.invoke()
            PlayerState.ERROR -> playerState.error?.let { onError?.invoke(it) }
            else -> {}
        }
    }
    
    Box(modifier = modifier) {
        AndroidView(
            factory = { _ ->
                playerState.getAlphaVideoView() ?: throw IllegalStateException("AlphaVideoView not initialized")
            },
            update = { _ ->
                // 更新视图配置
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // 自定义控制器
        if (playerState.showControls) {
            PlayerControls(
                playerState = playerState,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

/**
 * 简化版 AlphaPlayer 播放器组件
 */
@Composable
fun SimpleAlphaPlayer(
    videoPath: String,
    modifier: Modifier = Modifier,
    alphaPath: String? = null,
    videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_SURFACE_VIEW,
    autoPlay: Boolean = true,
    looping: Boolean = false,
    showControls: Boolean = true,
    onPlayerReady: (() -> Unit)? = null,
    onError: ((PlayerError) -> Unit)? = null
) {
    AlphaPlayer(
        modifier = modifier,
        videoPath = videoPath,
        alphaPath = alphaPath,
        videoViewType = videoViewType,
        config = PlayerConfig(
            autoPlay = autoPlay,
            looping = looping,
            showControls = showControls
        ),
        onPlayerReady = onPlayerReady,
        onError = onError
    )
}

/**
 * AlphaPlayer 播放器组件（仅视频路径）
 */
@Composable
fun AlphaPlayer(
    videoPath: String,
    modifier: Modifier = Modifier,
    videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_SURFACE_VIEW,
    config: PlayerConfig = PlayerConfig(),
    onPlayerReady: (() -> Unit)? = null,
    onError: ((PlayerError) -> Unit)? = null
) {
    AlphaPlayer(
        modifier = modifier,
        videoPath = videoPath,
        alphaPath = null,
        videoViewType = videoViewType,
        config = config,
        onPlayerReady = onPlayerReady,
        onError = onError
    )
}

/**
 * AlphaPlayer 播放器组件（带 Alpha 通道）
 */
@Composable
fun AlphaPlayerWithAlpha(
    videoPath: String,
    alphaPath: String,
    modifier: Modifier = Modifier,
    videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_SURFACE_VIEW,
    config: PlayerConfig = PlayerConfig(),
    onPlayerReady: (() -> Unit)? = null,
    onError: ((PlayerError) -> Unit)? = null
) {
    AlphaPlayer(
        modifier = modifier,
        videoPath = videoPath,
        alphaPath = alphaPath,
        videoViewType = videoViewType,
        config = config,
        onPlayerReady = onPlayerReady,
        onError = onError
    )
}
