package com.flutterup.players.compose

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.flutterup.players.view.AlphaExoPlayerView
import com.ss.ugc.android.alpha_player.IMonitor
import com.ss.ugc.android.alpha_player.IPlayerAction
import com.ss.ugc.android.alpha_player.model.AlphaVideoViewType

/**
 * AlphaPlayer 播放器 Compose 组件
 */
@Composable
fun AlphaPlayer(
    modifier: Modifier = Modifier,
    playerState: AlphaPlayerState = rememberAlphaPlayerState(),
    videoPath: String? = null,
    videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_SURFACE_VIEW,
    config: PlayerConfig = PlayerConfig(),
    onVideoSizeChanged: AlphaPlayerOnVideoSizeChanged? = null,
    onStartAction: AlphaPlayerOnStartAction? = null,
    onEndAction: AlphaPlayerOnEndAction? = null,
    onMonitor: AlphaPlayerOnMonitor? = null,
    onPlayerReady: (() -> Unit)? = null,
    onError: ((PlayerError) -> Unit)? = null
) {
    val context = LocalContext.current
    
    // 更新配置
    LaunchedEffect(config) {
        playerState.updateConfig(config)
    }
    
    // 设置视频源
    LaunchedEffect(videoPath) {
        if (videoPath != null) {
            playerState.setVideoSource(videoPath)
        } else {
            playerState.stop()
        }
    }
    
    // 监听播放器状态变化
    LaunchedEffect(playerState.playerState) {
        when (playerState.playerState) {
            PlayerState.READY -> onPlayerReady?.invoke()
            PlayerState.ERROR -> playerState.error?.let { onError?.invoke(it) }
            else -> {}
        }
    }

    LaunchedEffect(onVideoSizeChanged, onStartAction, onEndAction, onMonitor) {
        playerState.setOnVideoSizeChangedListener(onVideoSizeChanged)
        playerState.setOnStartActionListener(onStartAction)
        playerState.setOnEndActionListener(onEndAction)
        playerState.setOnMonitorListener(onMonitor)
    }
    
    Box(modifier = modifier) {
        AndroidView(
            factory = { _ ->
                AlphaExoPlayerView(context).apply {
                    playerState.updatePlayerView(this)
                }
            },
            update = { alphaPlayerView ->
                playerState.initialize(videoViewType)

                // 确保视图已附加
                if (playerState.isInitialized && playerState.currentVideoPath != null) {
                    alphaPlayerView.attachView()
                }
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // 自定义控制器
        if (playerState.showControls) {
            PlayerControls(
                playerState = playerState,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

/**
 * 简化版 AlphaPlayer 播放器组件
 */
@Composable
fun SimpleAlphaPlayer(
    videoPath: String,
    modifier: Modifier = Modifier,
    videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_TEXTURE_VIEW,
    autoPlay: Boolean = true,
    looping: Boolean = false,
    showControls: Boolean = true,
    onVideoSizeChanged: AlphaPlayerOnVideoSizeChanged? = null,
    onStartAction: AlphaPlayerOnStartAction? = null,
    onEndAction: AlphaPlayerOnEndAction? = null,
    onMonitor: AlphaPlayerOnMonitor? = null,
    onPlayerReady: (() -> Unit)? = null,
    onError: ((PlayerError) -> Unit)? = null
) {
    AlphaPlayer(
        modifier = modifier,
        videoPath = videoPath,
        videoViewType = videoViewType,
        config = PlayerConfig(
            autoPlay = autoPlay,
            looping = looping,
            showControls = showControls
        ),
        onVideoSizeChanged = onVideoSizeChanged,
        onStartAction = onStartAction,
        onEndAction = onEndAction,
        onMonitor = onMonitor,
        onPlayerReady = onPlayerReady,
        onError = onError
    )
}

/**
 * AlphaPlayer 播放器组件（仅视频路径）
 */
@Composable
fun AlphaPlayer(
    videoPath: String,
    modifier: Modifier = Modifier,
    videoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_TEXTURE_VIEW,
    config: PlayerConfig = PlayerConfig(),
    onVideoSizeChanged: AlphaPlayerOnVideoSizeChanged? = null,
    onStartAction: AlphaPlayerOnStartAction? = null,
    onEndAction: AlphaPlayerOnEndAction? = null,
    onMonitor: AlphaPlayerOnMonitor? = null,
    onPlayerReady: (() -> Unit)? = null,
    onError: ((PlayerError) -> Unit)? = null
) {
    AlphaPlayer(
        modifier = modifier,
        videoPath = videoPath,
        videoViewType = videoViewType,
        config = config,
        onVideoSizeChanged = onVideoSizeChanged,
        onStartAction = onStartAction,
        onEndAction = onEndAction,
        onMonitor = onMonitor,
        onPlayerReady = onPlayerReady,
        onError = onError
    )
}


