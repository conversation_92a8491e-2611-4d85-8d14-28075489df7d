package com.ss.ugc.android.alphavideoplayer.player

import android.content.Context
import android.util.Log
import android.view.Surface
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.VideoSize
import androidx.media3.exoplayer.ExoPlayer
import com.ss.ugc.android.alpha_player.model.VideoInfo
import com.ss.ugc.android.alpha_player.player.AbsPlayer

class AlphaExoPlayerImpl(private val context: Context) : AbsPlayer(context) {

    companion object {
        private const val PLAYER_TYPE = "ExoPlayer"
    }

    private lateinit var player: ExoPlayer

    private var videoSource: MediaItem? = null

    private var currVideoWidth: Int = 0

    private var currVideoHeight: Int = 0

    private val playerListener = object : Player.Listener {

        @Volatile
        private var isPlayWhenReady = false

        override fun onPlayerError(error: PlaybackException) {
            errorListener?.onError(0, 0, "ExoPlayer on error: " + Log.getStackTraceString(error))
        }

        override fun onPlayWhenReadyChanged(playWhenReady: <PERSON><PERSON><PERSON>, reason: Int) {
            isPlayWhenReady = playWhenReady
        }

        override fun onPlaybackStateChanged(playbackState: Int) {
            when (playbackState) {
                Player.STATE_READY -> {
                    if (isPlayWhenReady) {
                        preparedListener?.onPrepared()
                    }
                }
                Player.STATE_ENDED -> {
                    completionListener?.onCompletion()
                }
                else -> {}
            }
        }

        override fun onVideoSizeChanged(videoSize: VideoSize) {
            currVideoWidth = videoSize.width
            currVideoHeight = videoSize.height
        }

        override fun onRenderedFirstFrame() {
            firstFrameListener?.onFirstFrame()
        }
    }

    override fun getPlayerType(): String = PLAYER_TYPE


    override fun initMediaPlayer() {
        player = ExoPlayer.Builder(context).build()
        player.addListener(playerListener)
    }

    override fun setSurface(surface: Surface) {
        player.setVideoSurface(surface)
    }

    override fun setDataSource(dataPath: String) {
        videoSource = MediaItem.fromUri(dataPath)
        reset()
    }

    override fun prepareAsync() {
        videoSource?.let {
            player.setMediaItem(it)
            player.playWhenReady = true
            player.prepare()
        }
    }

    override fun start() {
        player.playWhenReady = true
    }

    override fun pause() {
        player.playWhenReady = false
    }

    override fun stop() {
        player.stop()
    }

    override fun reset() {
        player.stop()

        for (i in 0 until player.mediaItemCount) {
            player.removeMediaItem(i)
        }
    }

    override fun release() {
        player.release()
    }

    override fun setLooping(looping: Boolean) {
        player.repeatMode = if (looping) Player.REPEAT_MODE_ONE else Player.REPEAT_MODE_OFF
    }

    override fun setScreenOnWhilePlaying(onWhilePlaying: Boolean) {
    }

    override fun getVideoInfo(): VideoInfo {
        return VideoInfo(currVideoWidth, currVideoHeight)
    }
}