package com.flutterup.players.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.lifecycle.LifecycleOwner
import com.flutterup.players.databinding.ViewAlphaExoPlayerBinding
import com.flutterup.players.impl.AlphaExoPlayerImpl
import com.ss.ugc.android.alpha_player.IMonitor
import com.ss.ugc.android.alpha_player.IPlayerAction
import com.ss.ugc.android.alpha_player.controller.IPlayerController
import com.ss.ugc.android.alpha_player.controller.PlayerController
import com.ss.ugc.android.alpha_player.model.AlphaVideoViewType
import com.ss.ugc.android.alpha_player.model.Configuration
import com.ss.ugc.android.alpha_player.model.DataSource
import com.ss.ugc.android.alpha_player.model.ScaleType

class AlphaExoPlayerView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    private val binding = ViewAlphaExoPlayerBinding.inflate(LayoutInflater.from(context), this, true)

    private var controller: IPlayerController? = null

    private var playerImpl: AlphaExoPlayerImpl? = null

    /**
     * 初始化播放器控制器
     * @param context 上下文
     * @param owner 生命周期所有者
     * @param alphaVideoViewType 视频视图类型
     * @param playerAction 播放器动作
     * @param monitor 监听器
     */
    fun initPlayerController(
        context: Context,
        owner: LifecycleOwner,
        alphaVideoViewType: AlphaVideoViewType = AlphaVideoViewType.GL_TEXTURE_VIEW,
        playerAction: IPlayerAction = IPlayerAction.DEFAULT,
        monitor: IMonitor = IMonitor.DEFAULT
    ) {
        val configuration = Configuration(context, owner)
        //  GLTextureView supports custom display layer, but GLSurfaceView has better performance, and the GLSurfaceView is default.
        configuration.alphaVideoViewType = alphaVideoViewType
        //  You can implement your IMediaPlayer, here we use ExoPlayerImpl that implemented by ExoPlayer, and
        //  we support DefaultSystemPlayer as default player.
        playerImpl = AlphaExoPlayerImpl(context)

        controller = PlayerController.Companion.get(configuration, playerImpl)
        controller?.let {
            it.setPlayerAction(playerAction)
            it.setMonitor(monitor)
        }
    }

    fun startVideo(filePath: String) {
        if (TextUtils.isEmpty(filePath)) {
            return
        }

        var removedPath = filePath

        val cacheDir = context.cacheDir

        if (filePath.contains(cacheDir.path)) {
            removedPath = removedPath.substring(cacheDir.path.length + 1) // 移除缓存目录路径
        }

        val dataSource = DataSource()
            .setBaseDir(cacheDir.path)
            .setPortraitPath(removedPath, ScaleType.ScaleAspectFitCenter.ordinal)
            .setLandscapePath(removedPath, ScaleType.ScaleAspectFitCenter.ordinal)
            .setLooping(false)
        if (dataSource.isValid()) {
            startDataSource(dataSource)
        }
    }

    private fun startDataSource(dataSource: DataSource) {
        controller?.start(dataSource)
    }

    fun attachView() {
        controller?.attachAlphaView(binding.playerContainer)
    }

    fun detachView() {
        controller?.detachAlphaView(binding.playerContainer)
    }

    fun releasePlayerController() {
        controller?.let {
            it.detachAlphaView(binding.playerContainer)
            it.release()
        }
    }
}