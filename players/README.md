# Players Module - Compose 播放器封装

这是一个为 FlutterUp 应用提供的播放器模块，支持 Media3 和 AlphaPlayer 的 Compose 封装。

## 🎉 实现完成

✅ **Media3 播放器 Compose 封装** - 完整的 ExoPlayer Compose 集成
✅ **AlphaPlayer 播放器 Compose 封装** - 透明视频播放支持
✅ **统一播放器状态管理** - 响应式状态管理系统
✅ **播放器控制组件** - 可定制的播放器控制界面
✅ **生命周期管理** - 自动处理播放器生命周期
✅ **工具类和辅助函数** - 完整的工具支持
✅ **详细文档和示例** - 完整的使用指南

## 特性

- 🎬 **Media3 支持**: 完整的 ExoPlayer Compose 封装
- 🎭 **AlphaPlayer 支持**: 透明视频播放的 Compose 封装
- 🎮 **统一控制**: 提供统一的播放器控制组件
- 🔄 **生命周期管理**: 自动处理播放器生命周期
- 🎯 **状态管理**: 响应式的播放器状态管理
- 🎨 **可定制**: 支持自定义播放器控制器和样式
- 📱 **Compose 原生**: 完全基于 Jetpack Compose 构建

## 依赖

模块已包含以下依赖：

```kotlin
implementation(libs.media3.exoplayer)
implementation(libs.media3.exoplayer.dash)
implementation(libs.media3.ui)
implementation(libs.media3.ui.compose)
implementation(libs.alphaplayer)
```

## 快速开始

### 1. Media3 播放器

#### 基本使用

```kotlin
@Composable
fun VideoScreen() {
    SimpleMedia3Player(
        mediaUri = "https://example.com/video.mp4",
        modifier = Modifier.fillMaxSize(),
        autoPlay = true,
        looping = false,
        showControls = true,
        onPlayerReady = {
            // 播放器准备完成
        },
        onError = { error ->
            // 处理播放错误
            Log.e("Player", "Error: ${error.message}")
        }
    )
}
```

#### 高级使用

```kotlin
@Composable
fun AdvancedVideoScreen() {
    val playerState = rememberMedia3PlayerState()
    
    LaunchedEffect(Unit) {
        // 设置媒体源
        playerState.setMediaItem("https://example.com/video.mp4")
    }
    
    Media3Player(
        playerState = playerState,
        config = PlayerConfig(
            autoPlay = true,
            looping = false,
            showControls = true,
            muted = false
        ),
        modifier = Modifier.fillMaxSize(),
        onPlayerReady = {
            // 播放器准备完成
        },
        onError = { error ->
            // 处理错误
        }
    )
}
```

#### 使用 MediaItem

```kotlin
@Composable
fun MediaItemVideoScreen() {
    val mediaItem = remember {
        PlayerUtils.createMediaItem(
            uri = "https://example.com/video.mp4",
            title = "示例视频",
            subtitle = "这是一个示例视频",
            description = "视频描述"
        )
    }
    
    Media3Player(
        mediaItem = mediaItem,
        modifier = Modifier.fillMaxSize()
    )
}
```

### 2. AlphaPlayer 播放器

#### 基本使用

```kotlin
@Composable
fun AlphaVideoScreen() {
    SimpleAlphaPlayer(
        videoPath = "https://example.com/video.mp4",
        modifier = Modifier.fillMaxSize(),
        autoPlay = true,
        looping = false,
        showControls = true,
        onPlayerReady = {
            // 播放器准备完成
        },
        onError = { error ->
            // 处理播放错误
        }
    )
}
```

#### 带 Alpha 通道的视频

```kotlin
@Composable
fun TransparentVideoScreen() {
    AlphaPlayerWithAlpha(
        videoPath = "https://example.com/video.mp4",
        alphaPath = "https://example.com/alpha.mp4",
        videoViewType = AlphaVideoViewType.GL_SURFACE_VIEW,
        modifier = Modifier.fillMaxSize(),
        config = PlayerConfig(
            autoPlay = true,
            looping = true
        )
    )
}
```

#### 高级使用

```kotlin
@Composable
fun AdvancedAlphaVideoScreen() {
    val playerState = rememberAlphaPlayerState()
    
    LaunchedEffect(Unit) {
        // 设置视频源
        playerState.setVideoSource(
            videoPath = "https://example.com/video.mp4",
            alphaPath = "https://example.com/alpha.mp4"
        )
    }
    
    AlphaPlayer(
        playerState = playerState,
        videoViewType = AlphaVideoViewType.GL_SURFACE_VIEW,
        config = PlayerConfig(
            autoPlay = true,
            looping = false,
            showControls = true
        ),
        modifier = Modifier.fillMaxSize()
    )
}
```

### 3. 自定义播放器控制

#### 使用内置控制器

```kotlin
@Composable
fun CustomControlsScreen() {
    val playerState = rememberMedia3PlayerState()
    
    Box(modifier = Modifier.fillMaxSize()) {
        Media3Player(
            playerState = playerState,
            config = PlayerConfig(showControls = false), // 隐藏默认控制器
            modifier = Modifier.fillMaxSize()
        )
        
        // 自定义控制器
        PlayerControls(
            playerState = playerState,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
        
        // 中央播放按钮
        CenterPlayButton(
            playerState = playerState,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}
```

#### 简化控制器

```kotlin
@Composable
fun SimpleControlsScreen() {
    val playerState = rememberMedia3PlayerState()
    
    Box(modifier = Modifier.fillMaxSize()) {
        Media3Player(
            playerState = playerState,
            config = PlayerConfig(showControls = false),
            modifier = Modifier.fillMaxSize()
        )
        
        SimplePlayerControls(
            playerState = playerState,
            modifier = Modifier.align(Alignment.BottomCenter)
        )
    }
}
```

### 4. 播放器状态监听

```kotlin
@Composable
fun PlayerStateScreen() {
    val playerState = rememberMedia3PlayerState()
    
    // 监听播放器状态
    PlayerStateListener(
        playerState = playerState,
        onStateChange = { state ->
            when (state) {
                PlayerState.PLAYING -> Log.d("Player", "开始播放")
                PlayerState.PAUSED -> Log.d("Player", "暂停播放")
                PlayerState.ENDED -> Log.d("Player", "播放结束")
                PlayerState.ERROR -> Log.e("Player", "播放错误")
                else -> {}
            }
        },
        onProgressChange = { progress ->
            Log.d("Player", "播放进度: ${(progress * 100).toInt()}%")
        }
    )
    
    Media3Player(
        playerState = playerState,
        mediaUri = "https://example.com/video.mp4",
        modifier = Modifier.fillMaxSize()
    )
}
```

### 5. 播放器配置

#### 使用配置构建器

```kotlin
@Composable
fun ConfiguredPlayerScreen() {
    val config = playerConfig {
        autoPlay(true)
        looping(false)
        muted(false)
        showControls(true)
        enableLogging(true)
    }
    
    Media3Player(
        mediaUri = "https://example.com/video.mp4",
        config = config,
        modifier = Modifier.fillMaxSize()
    )
}
```

#### 动态配置更新

```kotlin
@Composable
fun DynamicConfigScreen() {
    val playerState = rememberMedia3PlayerState()
    var isLooping by remember { mutableStateOf(false) }
    
    Column {
        Switch(
            checked = isLooping,
            onCheckedChange = { 
                isLooping = it
                playerState.setLooping(it)
            }
        )
        
        Media3Player(
            playerState = playerState,
            mediaUri = "https://example.com/video.mp4",
            modifier = Modifier.fillMaxSize()
        )
    }
}
```

## 工具类

### PlayerUtils

```kotlin
// 创建 MediaItem
val mediaItem = PlayerUtils.createMediaItem(
    uri = "https://example.com/video.mp4",
    title = "视频标题"
)

// 格式化时间
val formattedTime = PlayerUtils.formatTime(120000) // "02:00"

// 检查视频格式
val isSupported = PlayerUtils.isSupportedVideoFormat("video.mp4")

// 推荐播放器类型
val playerType = PlayerUtils.recommendPlayerType(
    videoUrl = "https://example.com/video.mp4",
    alphaUrl = "https://example.com/alpha.mp4"
)
```

## 最佳实践

1. **生命周期管理**: 播放器会自动处理生命周期，在 Activity/Fragment 暂停时自动暂停播放
2. **内存管理**: 播放器在组件销毁时会自动释放资源
3. **错误处理**: 始终提供错误处理回调来处理播放异常
4. **性能优化**: 对于 AlphaPlayer，根据设备性能选择合适的 VideoViewType
5. **状态管理**: 使用 `rememberPlayerState` 来管理播放器状态，避免重复创建

## 注意事项

- AlphaPlayer 的进度控制可能有限制，具体取决于底层实现
- 使用 AlphaPlayer 时，确保视频格式和 Alpha 通道格式兼容
- Media3Player 支持更多的媒体格式和功能
- 在使用网络视频时，确保应用有网络权限
- 当前版本的播放器控制组件使用简化的图标，可根据需要自定义
- AlphaPlayer 实现基于 ExoPlayer，但某些高级功能可能需要额外配置

## 已知限制

- 播放器控制组件中的音量控制图标暂时不可用，需要手动添加合适的图标
- AlphaPlayer 的某些监听器功能可能需要根据实际 AlphaPlayer SDK 版本进行调整
- 进度控制在 AlphaPlayer 中可能不完全支持

## 架构

```
players/
├── compose/
│   ├── PlayerState.kt          # 播放器状态基类
│   ├── Media3PlayerState.kt    # Media3 播放器状态
│   ├── Media3Player.kt         # Media3 播放器组件
│   ├── AlphaPlayerState.kt     # AlphaPlayer 播放器状态
│   ├── AlphaPlayer.kt          # AlphaPlayer 播放器组件
│   ├── PlayerControls.kt       # 播放器控制组件
│   └── PlayerUtils.kt          # 播放器工具类
└── impl/
    └── AlphaExoPlayerImpl.kt   # AlphaPlayer ExoPlayer 实现
```
